import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["dropzone", "input", "preview", "progress", "error", "submit", "fileList"]

  connect() {
    this.files = []
    this.maxSize = 50 * 1024 * 1024 // 50MB default
    this.acceptedTypes = ['.csv', '.xlsx', '.xls', '.json', '.txt']
    this.maxFiles = 5

    console.log('File upload controller connected. Accepted types:', this.acceptedTypes)
    this.setupDropzone()
  }

  setupDropzone() {
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.dropzoneTarget.addEventListener(eventName, this.preventDefaults, false)
      document.body.addEventListener(eventName, this.preventDefaults, false)
    })

    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
      this.dropzoneTarget.addEventListener(eventName, this.highlight.bind(this), false)
    })

    ['dragleave', 'drop'].forEach(eventName => {
      this.dropzoneTarget.addEventListener(eventName, this.unhighlight.bind(this), false)
    })

    // Handle dropped files
    this.dropzoneTarget.addEventListener('drop', this.handleDrop.bind(this), false)
  }

  preventDefaults(e) {
    e.preventDefault()
    e.stopPropagation()
  }

  highlight() {
    this.dropzoneTarget.classList.add('border-indigo-500', 'bg-indigo-50', 'scale-105')
    this.dropzoneTarget.classList.remove('border-gray-300')
  }

  unhighlight() {
    this.dropzoneTarget.classList.remove('border-indigo-500', 'bg-indigo-50', 'scale-105')
    this.dropzoneTarget.classList.add('border-gray-300')
  }

  handleDrop(e) {
    const dt = e.dataTransfer
    const files = dt.files
    this.handleFiles(files)
  }

  // Handle file input change
  inputChanged() {
    this.handleFiles(this.inputTarget.files)
  }

  handleFiles(fileList) {
    this.clearError()

    // Convert FileList to Array
    const newFiles = Array.from(fileList)

    // Validate files
    const validationResult = this.validateFiles(newFiles)
    if (!validationResult.valid) {
      this.showError(validationResult.error)
      return
    }

    // Add files to our collection
    this.files = [...this.files, ...newFiles].slice(0, this.maxFilesValue)

    // Update preview
    this.updatePreview()
    this.updateSubmitButton()
  }

  validateFiles(files) {
    // Check file count
    if (this.files.length + files.length > this.maxFiles) {
      return {
        valid: false,
        error: `Maximum ${this.maxFiles} files allowed`
      }
    }

    // Check each file
    for (const file of files) {
      // Check file size
      if (file.size > this.maxSize) {
        return {
          valid: false,
          error: `File "${file.name}" is too large. Maximum size is ${this.formatFileSize(this.maxSize)}`
        }
      }

      // Check file type
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
      console.log(`Checking file: ${file.name}, extension: ${fileExtension}, accepted types:`, this.acceptedTypes)
      
      if (!this.acceptedTypes.includes(fileExtension)) {
        console.log('File type not accepted!')
        return {
          valid: false,
          error: `File type "${fileExtension}" is not supported. Accepted types: ${this.acceptedTypes.join(', ')}`
        }
      }
    }

    return { valid: true }
  }

  updatePreview() {
    if (this.files.length === 0) {
      if (this.hasPreviewTarget) {
        this.previewTarget.classList.add('hidden')
      }
      if (this.hasFileListTarget) {
        this.fileListTarget.innerHTML = ''
      }
      return
    }

    if (this.hasPreviewTarget) {
      this.previewTarget.classList.remove('hidden')
    }

    if (this.hasFileListTarget) {
      const previewHTML = this.files.map((file, index) => `
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 animate-fade-in" data-file-index="${index}">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              ${this.getFileIcon(file)}
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">${file.name}</p>
              <p class="text-sm text-gray-500">${this.formatFileSize(file.size)}</p>
            </div>
          </div>
          <button type="button"
                  class="text-red-600 hover:text-red-800 transition-colors p-1 rounded-full hover:bg-red-50"
                  data-action="click->file-upload#removeFile"
                  data-file-index="${index}"
                  title="Remove file">
            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      `).join('')

      this.fileListTarget.innerHTML = previewHTML
    }
  }

  removeFile(event) {
    const index = parseInt(event.currentTarget.dataset.fileIndex)
    this.files.splice(index, 1)
    this.updatePreview()
    this.updateSubmitButton()
    this.clearError()
  }

  updateSubmitButton() {
    if (this.hasSubmitTarget) {
      this.submitTarget.disabled = this.files.length === 0
      this.submitTarget.classList.toggle('opacity-50', this.files.length === 0)
      this.submitTarget.classList.toggle('cursor-not-allowed', this.files.length === 0)
    }
  }

  getFileIcon(file) {
    const extension = file.name.split('.').pop().toLowerCase()

    switch (extension) {
      case 'xlsx':
      case 'xls':
        return `<svg class="h-8 w-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 18h12V6h-4V2H4v16zm6-10h2v2h-2V8zm0 4h2v2h-2v-2z"/>
                </svg>`
      case 'csv':
        return `<svg class="h-8 w-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2z"/>
                </svg>`
      case 'json':
        return `<svg class="h-8 w-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4z"/>
                </svg>`
      case 'txt':
        return `<svg class="h-8 w-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4z"/>
                </svg>`
      default:
        return `<svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>`
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  showError(message) {
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = message
      this.errorTarget.classList.remove('hidden')
    } else {
      // Fallback: create error element
      let errorDiv = this.element.querySelector('.file-upload-error')

      if (!errorDiv) {
        errorDiv = document.createElement('div')
        errorDiv.className = 'file-upload-error bg-red-50 border border-red-200 rounded-md p-3 mt-3'
        errorDiv.innerHTML = `
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 error-message"></p>
            </div>
          </div>
        `
        this.dropzoneTarget.parentNode.appendChild(errorDiv)
      }

      errorDiv.querySelector('.error-message').textContent = message

      // Auto-hide error after 5 seconds
      setTimeout(() => {
        if (errorDiv.parentNode) {
          errorDiv.parentNode.removeChild(errorDiv)
        }
      }, 5000)
    }
  }

  clearError() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.add('hidden')
    }

    // Also clear fallback error
    const errorDiv = this.element.querySelector('.file-upload-error')
    if (errorDiv && errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv)
    }
  }

  showProgress() {
    if (this.hasProgressTarget) {
      this.progressTarget.classList.remove('hidden')
    }
    if (this.hasSubmitTarget) {
      this.submitTarget.disabled = true
      this.submitTarget.textContent = 'Uploading...'
    }
  }

  hideProgress() {
    if (this.hasProgressTarget) {
      this.progressTarget.classList.add('hidden')
    }
    if (this.hasSubmitTarget) {
      this.submitTarget.disabled = false
      this.submitTarget.textContent = 'Upload Files'
    }
  }
}