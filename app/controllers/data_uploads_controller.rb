class DataUploadsController < ApplicationController
  before_action :set_data_upload, only: [:show, :destroy, :process_upload, :download_presentation, :retry, :cancel, :start_processing]

  def index
    @data_uploads = Current.user.data_uploads.recent.includes(:data_result, :presentations)
    
    # Apply filters
    @data_uploads = @data_uploads.where("name ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    @data_uploads = @data_uploads.where(status: params[:status]) if params[:status].present?
    
    if params[:file_type].present?
      case params[:file_type]
      when 'excel'
        @data_uploads = @data_uploads.where(file_type: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'])
      when 'csv'
        @data_uploads = @data_uploads.where(file_type: 'text/csv')
      when 'text'
        @data_uploads = @data_uploads.where(file_type: 'text/plain')
      when 'json'
        @data_uploads = @data_uploads.where(file_type: 'application/json')
      end
    end
  end

  def update
    # Handle bulk actions
    if params[:bulk_action].present?
      handle_bulk_action(params[:bulk_action])
    else
      redirect_to data_uploads_path, alert: 'Unknown action.'
    end
  end

  def show
    @data_result = @data_upload.data_result
    @presentations = @data_upload.presentations.recent
  end

  def new
    @data_upload = Current.user.data_uploads.build
  end

  def create
    @data_upload = Current.user.data_uploads.build(data_upload_params)
    
    if params[:data_upload][:file].present?
      file = params[:data_upload][:file]
      @data_upload.name = file.original_filename
      @data_upload.file_type = file.content_type
      @data_upload.file_size = file.size
      @data_upload.status = 'uploaded'
      @data_upload.file.attach(file)
    end

    if @data_upload.save
      # Enqueue background processing job
      DataProcessingJob.perform_later(@data_upload.id)
      
      redirect_to @data_upload, notice: 'File uploaded successfully! Processing will begin shortly.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def process_upload
    if @data_upload.uploaded?
      DataProcessingJob.perform_later(@data_upload.id)
      redirect_to @data_upload, notice: 'Processing started!'
    else
      redirect_to @data_upload, alert: 'File cannot be processed in current state.'
    end
  end

  def download_presentation
    if @data_upload.presentations.any?
      presentation = @data_upload.presentations.recent.first
      if presentation.pdf_attached?
        redirect_to rails_blob_path(presentation.pdf_file, disposition: "attachment")
      else
        redirect_to @data_upload, alert: 'Presentation PDF not available.'
      end
    else
      redirect_to @data_upload, alert: 'No presentations available.'
    end
  end

  def retry
    if @data_upload.failed?
      @data_upload.update!(status: 'uploaded')
      DataProcessingJob.perform_later(@data_upload.id)
      redirect_to data_uploads_path, notice: 'Processing restarted!'
    else
      redirect_to data_uploads_path, alert: 'Can only retry failed uploads.'
    end
  end

  def cancel
    if @data_upload.processing?
      @data_upload.update!(status: 'failed')
      # TODO: Cancel any running background jobs
      redirect_to data_uploads_path, notice: 'Processing cancelled.'
    else
      redirect_to data_uploads_path, alert: 'Can only cancel processing uploads.'
    end
  end

  def start_processing
    if @data_upload.uploaded?
      @data_upload.update!(status: 'processing')
      DataProcessingJob.perform_later(@data_upload.id)
      redirect_to data_uploads_path, notice: 'Processing started!'
    else
      redirect_to data_uploads_path, alert: 'File cannot be processed in current state.'
    end
  end

  def destroy
    @data_upload.destroy
    redirect_to data_uploads_path, notice: 'Upload deleted successfully.'
  end

  private

  def set_data_upload
    @data_upload = Current.user.data_uploads.find(params[:id])
  end

  def data_upload_params
    params.require(:data_upload).permit(:name)
  end

  def handle_bulk_action(action)
    case action
    when 'retry_failed'
      failed_uploads = Current.user.data_uploads.failed
      count = failed_uploads.count
      failed_uploads.update_all(status: 'uploaded')
      failed_uploads.each { |upload| DataProcessingJob.perform_later(upload.id) }
      redirect_to data_uploads_path, notice: "Retrying #{count} failed uploads."
      
    when 'cancel_stalled'
      stalled_uploads = Current.user.data_uploads.where(status: 'processing').where('updated_at < ?', 10.minutes.ago)
      count = stalled_uploads.count
      stalled_uploads.update_all(status: 'failed')
      redirect_to data_uploads_path, notice: "Cancelled #{count} stalled uploads."
      
    when 'cleanup'
      cleanup_uploads = Current.user.data_uploads.where(status: ['failed', 'uploaded']).where('created_at < ?', 1.day.ago)
      count = cleanup_uploads.count
      cleanup_uploads.destroy_all
      redirect_to data_uploads_path, notice: "Deleted #{count} old uploads."
      
    else
      redirect_to data_uploads_path, alert: 'Unknown bulk action.'
    end
  end
end
