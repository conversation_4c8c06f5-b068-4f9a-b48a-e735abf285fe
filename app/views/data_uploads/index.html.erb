<% content_for :title, "Data Uploads - DataFlow" %>

<%= ui_container do %>
  <!-- Header -->
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Data Uploads
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Manage and track all your uploaded data files • <%= pluralize(@data_uploads.count, 'file') %> total
      </p>
    </div>
    <div class="mt-4 flex space-x-3 md:mt-0 md:ml-4">
      <%= ui_button "Upload New File", variant: :primary, onclick: "window.location.href='#{new_data_upload_path}'" do %>
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Upload New File
      <% end %>
    </div>
  </div>

  <!-- Filters and Search -->
  <%= ui_card title: "Filter & Search", class: "mb-6" do %>
    <%= form_tag data_uploads_path, method: :get, local: true,
        class: "grid grid-cols-1 md:grid-cols-4 gap-4",
        data: { controller: "auto-submit" } do %>

      <!-- Search -->
      <div class="md:col-span-2">
        <label for="search" class="form-label">Search files</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <%= text_field_tag :search, params[:search],
              placeholder: "Search by filename or description...",
              class: "form-input pl-10",
              data: { action: "input->auto-submit#submit" } %>
        </div>
      </div>

      <!-- Status Filter -->
      <div>
        <label for="status" class="form-label">Status</label>
        <%= select_tag :status,
            options_for_select([
              ['All statuses', ''],
              ['Uploaded', 'uploaded'],
              ['Processing', 'processing'],
              ['Completed', 'completed'],
              ['Failed', 'failed']
            ], params[:status]),
            class: "form-input",
            data: { action: "change->auto-submit#submit" } %>
      </div>

      <!-- File Type Filter -->
      <div>
        <label for="file_type" class="form-label">File Type</label>
        <%= select_tag :file_type,
            options_for_select([
              ['All types', ''],
              ['Excel Files', 'excel'],
              ['CSV Files', 'csv'],
              ['Text Files', 'text'],
              ['JSON Files', 'json']
            ], params[:file_type]),
            class: "form-input",
            data: { action: "change->auto-submit#submit" } %>
      </div>
    <% end %>

    <!-- Active Filters -->
    <% if params[:search].present? || params[:status].present? || params[:file_type].present? %>
      <div class="mt-4 flex flex-wrap items-center gap-2">
        <span class="text-sm text-gray-500">Active filters:</span>

        <% if params[:search].present? %>
          <%= ui_badge "Search: \"#{params[:search]}\"", variant: :info, class: "inline-flex items-center" do %>
            <%= params[:search] %>
            <%= link_to data_uploads_path(params.except(:search)), class: "ml-1 text-blue-600 hover:text-blue-800" do %>
              <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            <% end %>
          <% end %>
        <% end %>

        <% if params[:status].present? %>
          <%= ui_badge "Status: #{params[:status].humanize}", variant: :success, class: "inline-flex items-center" do %>
            <%= params[:status].humanize %>
            <%= link_to data_uploads_path(params.except(:status)), class: "ml-1 text-green-600 hover:text-green-800" do %>
              <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            <% end %>
          <% end %>
        <% end %>

        <% if params[:file_type].present? %>
          <%= ui_badge "Type: #{params[:file_type].humanize}", variant: :warning, class: "inline-flex items-center" do %>
            <%= params[:file_type].humanize %>
            <%= link_to data_uploads_path(params.except(:file_type)), class: "ml-1 text-yellow-600 hover:text-yellow-800" do %>
              <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            <% end %>
          <% end %>
        <% end %>

        <%= link_to "Clear all", data_uploads_path, class: "text-sm text-gray-500 hover:text-gray-700 underline" %>
      </div>
    <% end %>
  <% end %>

  <!-- Bulk Actions -->
  <% if @data_uploads.any? %>
    <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
      <div class="px-4 py-4 sm:px-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-sm font-medium text-gray-900">Bulk Actions</h3>
          <div class="flex items-center space-x-2">
            <% if @data_uploads.failed.any? %>
              <%= button_to "Retry All Failed", 
                  data_uploads_path, 
                  method: :patch,
                  params: { bulk_action: 'retry_failed' },
                  class: "text-xs text-blue-600 hover:text-blue-800 px-3 py-1 rounded border border-blue-300 hover:bg-blue-50",
                  title: "Retry all failed uploads",
                  data: { confirm: "Retry all failed uploads?" } %>
            <% end %>
            
            <% if @data_uploads.where(status: 'processing').where('updated_at < ?', 10.minutes.ago).any? %>
              <%= button_to "Cancel Stalled", 
                  data_uploads_path, 
                  method: :patch,
                  params: { bulk_action: 'cancel_stalled' },
                  class: "text-xs text-red-600 hover:text-red-800 px-3 py-1 rounded border border-red-300 hover:bg-red-50",
                  title: "Cancel all stalled uploads",
                  data: { confirm: "Cancel all stalled uploads?" } %>
            <% end %>
            
            <% if @data_uploads.where(status: ['failed', 'uploaded']).any? %>
              <%= button_to "Delete Failed & Unused", 
                  data_uploads_path, 
                  method: :patch,
                  params: { bulk_action: 'cleanup' },
                  class: "text-xs text-gray-600 hover:text-gray-800 px-3 py-1 rounded border border-gray-300 hover:bg-gray-50",
                  title: "Delete failed and unused uploads",
                  data: { confirm: "Delete all failed and unused uploads? This cannot be undone." } %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Upload List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @data_uploads.any? %>
      <ul role="list" class="divide-y divide-gray-200">
        <% @data_uploads.each do |upload| %>
          <li>
            <%= link_to data_upload_path(upload), class: "block hover:bg-gray-50 transition-colors duration-150" do %>
              <div class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <!-- File type icon -->
                      <% if upload.excel_file? %>
                        <svg class="h-10 w-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 18h12V6h-4V2H4v16zm6-10h2v2h-2V8zm0 4h2v2h-2v-2z"/>
                        </svg>
                      <% elsif upload.csv_file? %>
                        <svg class="h-10 w-10 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2z"/>
                        </svg>
                      <% elsif upload.json_file? %>
                        <svg class="h-10 w-10 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                        </svg>
                      <% else %>
                        <svg class="h-10 w-10 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      <% end %>
                    </div>
                    <div class="ml-4">
                      <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= upload.name %>
                        </p>
                        <!-- Processing indicator -->
                        <% if upload.status == 'processing' %>
                          <div class="ml-2 flex-shrink-0">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                          </div>
                        <% end %>
                      </div>
                      <div class="mt-1 flex items-center text-sm text-gray-500">
                        <p class="truncate">
                          <%= upload.file_type %> • <%= number_to_human_size(upload.file_size) %>
                        </p>
                        <span class="mx-2">•</span>
                        <p>
                          Uploaded <%= time_ago_in_words(upload.created_at) %> ago
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-4">
                    <!-- Status badge and actions -->
                    <div class="flex items-center space-x-2">
                      <% case upload.status %>
                      <% when 'completed' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                          </svg>
                          Completed
                        </span>
                      <% when 'processing' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                          </svg>
                          <% if upload.updated_at < 10.minutes.ago %>
                            Stalled (Processing > 10min)
                          <% else %>
                            Processing
                          <% end %>
                        </span>
                        <% if upload.updated_at < 10.minutes.ago %>
                          <%= button_to "Cancel", data_upload_path(upload, action: :cancel), 
                              method: :patch,
                              class: "text-xs text-red-600 hover:text-red-800 px-2 py-1 rounded border border-red-300 hover:bg-red-50",
                              title: "Cancel stalled processing",
                              data: { confirm: "Cancel this stalled upload?" } %>
                        <% end %>
                      <% when 'failed' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                          </svg>
                          Failed
                        </span>
                        <div class="flex items-center space-x-1">
                          <%= button_to "Retry", data_upload_path(upload, action: :retry), 
                              method: :patch,
                              class: "text-xs text-blue-600 hover:text-blue-800 px-2 py-1 rounded border border-blue-300 hover:bg-blue-50",
                              title: "Retry processing this file" %>
                          <%= button_to "Delete", data_upload_path(upload), 
                              method: :delete,
                              class: "text-xs text-red-600 hover:text-red-800 px-2 py-1 rounded border border-red-300 hover:bg-red-50",
                              title: "Delete this upload",
                              data: { confirm: "Delete this upload permanently?" } %>
                        </div>
                      <% when 'uploaded' %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                          </svg>
                          Uploaded
                        </span>
                        <div class="flex items-center space-x-1">
                          <%= button_to "Process", data_upload_path(upload, action: :start_processing), 
                              method: :patch,
                              class: "text-xs text-green-600 hover:text-green-800 px-2 py-1 rounded border border-green-300 hover:bg-green-50",
                              title: "Start processing this file" %>
                          <%= button_to "Delete", data_upload_path(upload), 
                              method: :delete,
                              class: "text-xs text-red-600 hover:text-red-800 px-2 py-1 rounded border border-red-300 hover:bg-red-50",
                              title: "Delete this upload",
                              data: { confirm: "Delete this upload permanently?" } %>
                        </div>
                      <% else %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                          </svg>
                          Unknown
                        </span>
                      <% end %>
                    </div>
                    
                    <!-- Presentations count -->
                    <% if upload.presentations.any? %>
                      <div class="flex items-center text-sm text-gray-500">
                        <svg class="mr-1 h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z"/>
                        </svg>
                        <%= pluralize(upload.presentations.count, 'presentation') %>
                      </div>
                    <% end %>
                    
                    <!-- Arrow icon -->
                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                
                <!-- Progress bar for processing files -->
                <% if upload.status == 'processing' %>
                  <div class="mt-3">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="bg-indigo-600 h-2 rounded-full animate-pulse" style="width: 65%"></div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Processing your data...</p>
                  </div>
                <% end %>
                
                <!-- Error message for failed uploads -->
                <% if upload.status == 'failed' %>
                  <div class="mt-3">
                    <p class="text-sm text-red-600">
                      Processing failed. Click to view details and retry.
                    </p>
                  </div>
                <% end %>
              </div>
            <% end %>
          </li>
        <% end %>
      </ul>
      
    <% else %>
      <!-- Empty state -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No uploads found</h3>
        <p class="mt-1 text-sm text-gray-500">
          <% if params[:search].present? || params[:status].present? || params[:file_type].present? %>
            No uploads match your current filters. Try adjusting your search criteria.
          <% else %>
            Get started by uploading your first data file.
          <% end %>
        </p>
        <div class="mt-6">
          <% if params[:search].present? || params[:status].present? || params[:file_type].present? %>
            <%= link_to data_uploads_path,
                class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
              Clear Filters
            <% end %>
          <% else %>
            <%= link_to new_data_upload_path,
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Upload Your First File
            <% end %>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
<% end %>
