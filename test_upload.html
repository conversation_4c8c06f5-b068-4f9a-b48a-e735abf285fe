<!DOCTYPE html>
<html>
<head>
    <title>Upload Test</title>
    <script src="https://unpkg.com/@hotwired/stimulus/dist/stimulus.min.js"></script>
    <style>
        .disabled { opacity: 0.5; cursor: not-allowed; }
        .preview { margin-top: 10px; padding: 10px; background: #f0f0f0; }
        .dropzone { border: 2px dashed #ccc; padding: 20px; margin: 10px 0; }
        .dropzone.highlight { border-color: #007bff; background: #e3f2fd; }
    </style>
</head>
<body>
    <h1>File Upload Test</h1>
    
    <div data-controller="file-upload">
        <div class="dropzone" data-file-upload-target="dropzone">
            <input type="file" 
                   data-file-upload-target="input" 
                   data-action="change->file-upload#inputChanged"
                   accept=".csv,.xlsx,.xls,.json,.txt">
            <p>Choose file or drag and drop</p>
        </div>
        
        <div data-file-upload-target="preview" class="preview" style="display: none;">
            <div data-file-upload-target="fileList"></div>
        </div>
        
        <button data-file-upload-target="submit" disabled>Upload and Process</button>
    </div>

    <script>
        // Simplified file upload controller for testing
        class FileUploadController extends Stimulus.Controller {
            static targets = ["dropzone", "input", "preview", "submit", "fileList"]
            
            connect() {
                this.files = []
                console.log("File upload controller connected")
                this.setupDropzone()
            }
            
            setupDropzone() {
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    this.dropzoneTarget.addEventListener(eventName, this.preventDefaults, false)
                })
                
                ['dragenter', 'dragover'].forEach(eventName => {
                    this.dropzoneTarget.addEventListener(eventName, this.highlight.bind(this), false)
                })
                
                ['dragleave', 'drop'].forEach(eventName => {
                    this.dropzoneTarget.addEventListener(eventName, this.unhighlight.bind(this), false)
                })
                
                this.dropzoneTarget.addEventListener('drop', this.handleDrop.bind(this), false)
            }
            
            preventDefaults(e) {
                e.preventDefault()
                e.stopPropagation()
            }
            
            highlight() {
                this.dropzoneTarget.classList.add('highlight')
            }
            
            unhighlight() {
                this.dropzoneTarget.classList.remove('highlight')
            }
            
            handleDrop(e) {
                const dt = e.dataTransfer
                const files = dt.files
                this.handleFiles(files)
            }
            
            inputChanged() {
                console.log("Input changed:", this.inputTarget.files)
                this.handleFiles(this.inputTarget.files)
            }
            
            handleFiles(fileList) {
                console.log("Handling files:", fileList)
                this.files = Array.from(fileList)
                this.updatePreview()
                this.updateSubmitButton()
            }
            
            updatePreview() {
                if (this.files.length === 0) {
                    this.previewTarget.style.display = 'none'
                    return
                }
                
                this.previewTarget.style.display = 'block'
                const fileHTML = this.files.map(file => `
                    <div>📄 ${file.name} (${this.formatFileSize(file.size)})</div>
                `).join('')
                this.fileListTarget.innerHTML = fileHTML
            }
            
            updateSubmitButton() {
                console.log("Updating submit button. Files:", this.files.length)
                this.submitTarget.disabled = this.files.length === 0
                this.submitTarget.classList.toggle('disabled', this.files.length === 0)
            }
            
            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes'
                const k = 1024
                const sizes = ['Bytes', 'KB', 'MB', 'GB']
                const i = Math.floor(Math.log(bytes) / Math.log(k))
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
            }
        }
        
        // Register the controller
        const application = Stimulus.Application.start()
        application.register("file-upload", FileUploadController)
    </script>
</body>
</html>